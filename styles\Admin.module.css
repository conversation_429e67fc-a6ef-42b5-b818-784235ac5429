.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.loginBox {
  max-width: 400px;
  margin: 100px auto;
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.loginBox h1 {
  margin-bottom: 30px;
  font-size: 1.8rem;
  font-weight: 600;
}

.input {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.button {
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0;
}

.stats {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 1.1rem;
}

.statsGrid {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.9rem;
}

.statsGrid span {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

.refreshButton {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.controls {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  align-items: center;
}

.searchInput {
  flex: 1;
  min-width: 300px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.select {
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
}

.select option {
  background: #1a1a2e;
  color: white;
}

.tableContainer {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 10px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
}

.table td {
  padding: 12px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  vertical-align: middle;
}

.table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

.idCell {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.usernameCell {
  font-weight: 600;
  color: #4ecdc4;
}

.levelCell {
  text-align: center;
}

.level {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.xpCell {
  font-weight: 600;
  color: #feca57;
}

.streak {
  background: rgba(78, 205, 196, 0.2);
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid rgba(78, 205, 196, 0.4);
}

.multiplayer {
  background: rgba(255, 107, 107, 0.2);
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid rgba(255, 107, 107, 0.4);
}

.achievements {
  font-size: 12px;
  white-space: nowrap;
}

.dateCell {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}

.statusCell {
  text-align: center;
  width: 50px;
}

.statusBadge {
  font-size: 16px;
}

.active {
  filter: drop-shadow(0 0 4px #4caf50);
}

.inactive {
  opacity: 0.5;
}

.winLoss {
  background: rgba(78, 205, 196, 0.2);
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid rgba(78, 205, 196, 0.4);
  font-weight: 600;
}

.historyCell {
  text-align: center;
  font-weight: 600;
  color: #feca57;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
}

.error {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.4);
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  text-align: center;
  color: #ff6b6b;
}

.noResults {
  text-align: center;
  padding: 60px 20px;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsividade */
@media (max-width: 1200px) {
  .table {
    font-size: 12px;
  }
  
  .table th,
  .table td {
    padding: 8px 6px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .searchInput {
    min-width: auto;
  }
  
  .tableContainer {
    overflow-x: auto;
  }
  
  .table {
    min-width: 800px;
  }
}
